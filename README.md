# Phishing URL Detection System

A complete web-based phishing detection system that analyzes URLs and classifies them as legitimate or phishing using machine learning.

## Features

- **Real-time URL Analysis**: Extract 30 specific features from any URL
- **Machine Learning Classification**: Uses a stacking ensemble model for accurate predictions
- **Web Interface**: Clean, responsive web interface for easy interaction
- **Feature Extraction**: Comprehensive analysis including:
  - URL structure analysis
  - Content analysis (HTML, CSS, JS)
  - Security features (HTTPS, certificates)
  - Social media presence
  - Page elements and references

## System Architecture

```
User Input (URL) → Feature Extractor → Preprocessor → ML Model → Classification Result
```

### Components

1. **Feature Extractor** (`feature_extractor.py`): Extracts 30 features from raw URLs
2. **FastAPI Backend** (`main.py`): Web API for handling requests and predictions
3. **Frontend** (`templates/`, `static/`): HTML, CSS, and JavaScript interface
4. **Models**: Pre-trained preprocessor and stacking model (joblib files)

## 30 Extracted Features

### URL Structure Features
- URLLength, TLD, URLSimilarityIndex, CharContinuationRate
- TLDLegitimateProb, NoOfLettersInURL, LetterRatioInURL
- NoOfDegitsInURL, NoOfOtherSpecialCharsInURL, IsHTTPS

### Content Analysis Features
- LineOfCode, LargestLineLength, HasTitle, Title
- DomainTitleMatchScore, URLTitleMatchScore, HasFavicon
- IsResponsive, HasDescription

### Page Elements Features
- NoOfiFrame, HasSocialNet, HasSubmitButton, HasHiddenFields
- HasCopyrightInfo, NoOfImage, NoOfCSS, NoOfJS

### Reference Features
- NoOfSelfRef, NoOfEmptyRef, NoOfExternalRef

## Installation

1. **Install Dependencies** (you mentioned you'll do this manually):
   ```bash
   pip install -r requirements.txt
   ```

2. **Verify Installation**:
   ```bash
   python test_system.py
   ```

## Usage

### Option 1: Quick Start
```bash
python run_server.py
```

### Option 2: Manual Start
```bash
python main.py
```

### Option 3: Using Uvicorn directly
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## Access the Application

1. Start the server using one of the methods above
2. Open your web browser
3. Navigate to: `http://localhost:8000`
4. Enter a URL (e.g., `https://www.google.com`) and click "Analyze URL"

## API Endpoints

### Web Interface
- `GET /` - Main web interface

### API Endpoints
- `POST /predict` - Analyze a URL and get classification
  ```json
  {
    "url": "https://example.com"
  }
  ```

- `GET /health` - Health check endpoint
- `GET /features/{url}` - Get extracted features for debugging

## Example API Usage

```python
import requests

# Analyze a URL
response = requests.post('http://localhost:8000/predict', 
                        json={'url': 'https://www.google.com'})
result = response.json()

print(f"Classification: {result['classification']}")
print(f"Probability: {result['probability']}")
```

## Response Format

```json
{
  "url": "https://www.google.com",
  "classification": "Legitimate",
  "probability": 0.9234,
  "features": {
    "URLLength": 23,
    "TLD": "com",
    "IsHTTPS": 1,
    ...
  }
}
```

## Classification Logic

- **Legitimate**: URLs classified as safe (probability closer to 1.0 for legitimate class)
- **Phishing**: URLs classified as malicious (probability closer to 1.0 for phishing class)

The system returns:
- `classification`: "Legitimate" or "Phishing"
- `probability`: Confidence score for the predicted class (0.0 to 1.0)

## File Structure

```
├── main.py                     # FastAPI application
├── feature_extractor.py        # URL feature extraction
├── run_server.py              # Server startup script
├── test_system.py             # System testing script
├── requirements.txt           # Python dependencies
├── features_preprocessor.joblib # Trained preprocessor
├── final_stacking_model.joblib # Trained ML model
├── templates/
│   └── index.html            # Web interface
├── static/
│   ├── style.css            # Styling
│   └── script.js            # Frontend logic
└── README.md                # This file
```

## Dependencies

- FastAPI: Web framework
- Uvicorn: ASGI server
- Requests: HTTP requests for URL analysis
- BeautifulSoup4: HTML parsing
- TLDExtract: Domain extraction
- Pandas: Data manipulation
- NumPy: Numerical operations
- Scikit-learn: Machine learning utilities
- Joblib: Model serialization

## Testing

Run the test script to verify everything is working:

```bash
python test_system.py
```

This will check:
- File structure
- Package imports
- Model loading
- Feature extraction

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed
   ```bash
   pip install -r requirements.txt
   ```

2. **Model Loading Errors**: Ensure `features_preprocessor.joblib` and `final_stacking_model.joblib` are in the root directory

3. **Port Already in Use**: Change the port in `main.py` or kill the existing process

4. **Network Errors**: Some features require internet access to analyze URLs

### Debug Mode

For debugging, you can access individual features:
```
GET http://localhost:8000/features/https://www.google.com
```

## Security Notes

- The system makes HTTP requests to analyze URLs
- Be cautious when analyzing suspicious URLs
- The system is designed for analysis, not for browsing malicious sites
- Consider running in a sandboxed environment for production use

## Performance

- Feature extraction typically takes 2-10 seconds depending on the target website
- The system handles timeouts gracefully
- Large websites may take longer to analyze due to content size

---

**Note**: This system is for educational and research purposes. Always exercise caution when dealing with potentially malicious URLs.
