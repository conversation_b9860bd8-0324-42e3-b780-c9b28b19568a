#!/usr/bin/env python3
"""
Test script to verify the phishing detection system works correctly
"""

import sys
import os

def test_imports():
    """Test if all required packages can be imported"""
    print("Testing imports...")
    
    try:
        import requests
        print("✓ requests")
    except ImportError as e:
        print(f"✗ requests: {e}")
        return False
    
    try:
        import pandas
        print("✓ pandas")
    except ImportError as e:
        print(f"✗ pandas: {e}")
        return False
    
    try:
        import numpy
        print("✓ numpy")
    except ImportError as e:
        print(f"✗ numpy: {e}")
        return False
    
    try:
        import joblib
        print("✓ joblib")
    except ImportError as e:
        print(f"✗ joblib: {e}")
        return False
    
    try:
        import fastapi
        print("✓ fastapi")
    except ImportError as e:
        print(f"✗ fastapi: {e}")
        return False
    
    try:
        import uvicorn
        print("✓ uvicorn")
    except ImportError as e:
        print(f"✗ uvicorn: {e}")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✓ beautifulsoup4")
    except ImportError as e:
        print(f"✗ beautifulsoup4: {e}")
        return False
    
    try:
        import tldextract
        print("✓ tldextract")
    except ImportError as e:
        print(f"✗ tldextract: {e}")
        return False
    
    return True

def test_models():
    """Test if models can be loaded"""
    print("\nTesting model loading...")
    
    try:
        import joblib
        
        if not os.path.exists('features_preprocessor.joblib'):
            print("✗ features_preprocessor.joblib not found")
            return False
        
        if not os.path.exists('final_stacking_model.joblib'):
            print("✗ final_stacking_model.joblib not found")
            return False
        
        preprocessor = joblib.load('features_preprocessor.joblib')
        print("✓ Preprocessor loaded")
        
        model = joblib.load('final_stacking_model.joblib')
        print("✓ Model loaded")
        
        return True
        
    except Exception as e:
        print(f"✗ Model loading failed: {e}")
        return False

def test_feature_extractor():
    """Test if feature extractor works"""
    print("\nTesting feature extractor...")
    
    try:
        from feature_extractor import URLFeatureExtractor
        
        extractor = URLFeatureExtractor()
        print("✓ Feature extractor initialized")
        
        # Test with a simple URL
        features = extractor.extract_features("https://www.google.com")
        
        expected_features = [
            'URLLength', 'TLD', 'URLSimilarityIndex', 'CharContinuationRate', 'TLDLegitimateProb',
            'NoOfLettersInURL', 'LetterRatioInURL', 'NoOfDegitsInURL', 'NoOfOtherSpecialCharsInURL',
            'IsHTTPS', 'LineOfCode', 'LargestLineLength', 'HasTitle', 'Title', 'DomainTitleMatchScore',
            'URLTitleMatchScore', 'HasFavicon', 'IsResponsive', 'HasDescription', 'NoOfiFrame',
            'HasSocialNet', 'HasSubmitButton', 'HasHiddenFields', 'HasCopyrightInfo', 'NoOfImage',
            'NoOfCSS', 'NoOfJS', 'NoOfSelfRef', 'NoOfEmptyRef', 'NoOfExternalRef'
        ]
        
        missing_features = [f for f in expected_features if f not in features]
        if missing_features:
            print(f"✗ Missing features: {missing_features}")
            return False
        
        print(f"✓ All 30 features extracted successfully")
        print(f"  Sample features: URLLength={features['URLLength']}, TLD={features['TLD']}, IsHTTPS={features['IsHTTPS']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Feature extraction failed: {e}")
        return False

def test_file_structure():
    """Test if all required files exist"""
    print("\nTesting file structure...")
    
    required_files = [
        'main.py',
        'feature_extractor.py',
        'requirements.txt',
        'templates/index.html',
        'static/style.css',
        'static/script.js',
        'features_preprocessor.joblib',
        'final_stacking_model.joblib'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
            all_exist = False
    
    return all_exist

def main():
    """Run all tests"""
    print("=" * 50)
    print("PHISHING DETECTION SYSTEM TEST")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Package Imports", test_imports),
        ("Model Loading", test_models),
        ("Feature Extraction", test_feature_extractor)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! The system is ready to run.")
        print("\nTo start the server, run:")
        print("python main.py")
        print("\nThen open your browser to: http://localhost:8000")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        print("\nMake sure to install requirements first:")
        print("pip install -r requirements.txt")

if __name__ == "__main__":
    main()
