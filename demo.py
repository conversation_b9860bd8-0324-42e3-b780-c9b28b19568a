#!/usr/bin/env python3
"""
Demo script to test the phishing detection system
"""

def demo_feature_extraction():
    """Demo the feature extraction process"""
    print("🔍 FEATURE EXTRACTION DEMO")
    print("=" * 50)

    try:
        from feature_extractor import URLFeatureExtractor

        extractor = URLFeatureExtractor()

        # Test URLs
        test_urls = [
            "https://www.google.com",
            "https://github.com",
            "http://example.com"
        ]

        for url in test_urls:
            print(f"\nAnalyzing: {url}")
            print("-" * 30)

            features = extractor.extract_features(url)

            # Show key features
            key_features = [
                'URLLength', 'TLD', 'IsHTTPS', 'HasTitle',
                'NoOfImage', 'NoOfCSS', 'NoOfJS'
            ]

            for feature in key_features:
                if feature in features:
                    print(f"{feature}: {features[feature]}")

            print(f"Total features extracted: {len(features)}")

        return True

    except Exception as e:
        print(f"Error in feature extraction demo: {e}")
        return False

def demo_model_loading():
    """Demo the model loading process"""
    print("\n🤖 MODEL LOADING DEMO")
    print("=" * 50)

    try:
        import joblib
        import pandas as pd

        # Load models
        print("Loading preprocessor...")
        preprocessor = joblib.load('features_preprocessor.joblib')
        print("✓ Preprocessor loaded")

        print("Loading model...")
        model = joblib.load('final_stacking_model.joblib')
        print("✓ Model loaded")

        # Test with dummy data
        print("\nTesting with sample data...")

        # Create sample feature vector (correct order: TLD, Title, then the rest)
        feature_names = [
            'URLLength', 'TLD', 'Title', 'URLSimilarityIndex', 'CharContinuationRate', 'TLDLegitimateProb',
            'NoOfLettersInURL', 'LetterRatioInURL', 'NoOfDegitsInURL', 'NoOfOtherSpecialCharsInURL',
            'IsHTTPS', 'LineOfCode', 'LargestLineLength', 'HasTitle', 'DomainTitleMatchScore',
            'URLTitleMatchScore', 'HasFavicon', 'IsResponsive', 'HasDescription', 'NoOfiFrame',
            'HasSocialNet', 'HasSubmitButton', 'HasHiddenFields', 'HasCopyrightInfo', 'NoOfImage',
            'NoOfCSS', 'NoOfJS', 'NoOfSelfRef', 'NoOfEmptyRef', 'NoOfExternalRef'
        ]

        # Sample legitimate URL features (adjusted for correct order)
        sample_features = [
            23, 'com', 'Google', 0.0, 0.0, 0.9, 15, 0.65, 0, 0, 1,
            100, 80, 1, 0.8, 0.3, 1, 1, 1, 0,
            1, 0, 0, 1, 5, 2, 3, 10, 0, 2
        ]

        df = pd.DataFrame([sample_features], columns=feature_names)

        # Preprocess
        processed = preprocessor.transform(df)
        print(f"✓ Features preprocessed: shape {processed.shape}")

        # Predict
        prediction = model.predict(processed)[0]
        probabilities = model.predict_proba(processed)[0]

        classification = "Legitimate" if prediction == 1 else "Phishing"
        confidence = probabilities[prediction]

        print(f"✓ Prediction: {classification}")
        print(f"✓ Confidence: {confidence:.4f}")

        return True

    except Exception as e:
        print(f"Error in model demo: {e}")
        return False

def demo_api_format():
    """Demo the expected API request/response format"""
    print("\n📡 API FORMAT DEMO")
    print("=" * 50)

    print("Request format:")
    print("POST /predict")
    print("Content-Type: application/json")
    print("""
{
    "url": "https://www.google.com"
}
""")

    print("Response format:")
    print("""
{
    "url": "https://www.google.com",
    "classification": "Legitimate",
    "probability": 0.9234,
    "features": {
        "URLLength": 23,
        "TLD": "com",
        "IsHTTPS": 1,
        "HasTitle": 1,
        "Title": "Google",
        ...
    }
}
""")

def main():
    """Run all demos"""
    print("🎯 PHISHING DETECTION SYSTEM DEMO")
    print("=" * 60)

    # Run demos
    demos = [
        ("Feature Extraction", demo_feature_extraction),
        ("Model Loading", demo_model_loading),
        ("API Format", demo_api_format)
    ]

    for demo_name, demo_func in demos:
        try:
            success = demo_func()
            if success is False:
                print(f"\n❌ {demo_name} demo failed")
            else:
                print(f"\n✅ {demo_name} demo completed")
        except Exception as e:
            print(f"\n❌ {demo_name} demo failed: {e}")

    print("\n" + "=" * 60)
    print("🚀 READY TO START THE SERVER!")
    print("=" * 60)
    print("\nTo start the web application:")
    print("1. Install requirements: pip install -r requirements.txt")
    print("2. Run: python run_server.py")
    print("3. Open: http://localhost:8000")
    print("\nOr run the test suite:")
    print("python test_system.py")

if __name__ == "__main__":
    main()
